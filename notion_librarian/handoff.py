import uuid
from typing import Annotated, cast
from langchain_core.tools import tool, BaseTool, InjectedToolCallId
from langchain_core.messages import ToolMessage, AIMessage
from langgraph.types import Command, Send
from langgraph.prebuilt import InjectedState
from langgraph_supervisor.handoff import WH<PERSON><PERSON>PACE_RE, METADATA_KEY_HANDOFF_DESTINATION


def _normalize_agent_name(agent_name: str) -> str:
    """Normalize an agent name to be used inside the tool name."""
    return WHITESPACE_RE.sub("_", agent_name.strip()).lower()


def _remove_non_handoff_tool_calls(
    last_ai_message: AIMessage, handoff_tool_call_id: str
) -> AIMessage:
    """Remove tool calls that are not meant for the agent."""
    # if the supervisor is calling multiple agents/tools in parallel,
    # we need to remove tool calls that are not meant for this agent
    # to ensure that the resulting message history is valid
    content = last_ai_message.content
    if isinstance(content, list) and len(content) > 1 and isinstance(content[0], dict):
        content = [
            content_block
            for content_block in content
            if (
                content_block["type"] == "tool_use"
                and content_block["id"] == handoff_tool_call_id
            )
            or content_block["type"] != "tool_use"
        ]

    last_ai_message = AIMessage(
        content=content,
        tool_calls=[
            tool_call
            for tool_call in last_ai_message.tool_calls
            if tool_call["id"] == handoff_tool_call_id
        ],
        name=last_ai_message.name,
        id=str(uuid.uuid4()),
    )
    return last_ai_message


def create_custom_handoff_tool(
    *,
    agent_name: str,
    name: str | None,
    description: str | None,
    add_handoff_messages: bool = True,
) -> BaseTool:
    if name is None:
        name = f"transfer_to_{_normalize_agent_name(agent_name)}"

    if description is None:
        description = f"Ask agent '{agent_name}' for help"

    @tool(name, description=description)
    def handoff_to_agent(
        task_description: Annotated[
            str,
            "Detailed description of what the next agent should do, including all of the relevant context.",
        ],
        state: Annotated[dict, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ):
        tool_message = ToolMessage(
            content=f"Successfully transferred to {agent_name}",
            name=name,
            tool_call_id=tool_call_id,
            response_metadata={METADATA_KEY_HANDOFF_DESTINATION: agent_name},
        )
        last_ai_message = cast(AIMessage, state["messages"][-1])
        # Handle parallel handoffs
        if len(last_ai_message.tool_calls) > 1:
            handoff_messages = state["messages"][:-1]
            if add_handoff_messages:
                handoff_messages.extend(
                    (
                        _remove_non_handoff_tool_calls(last_ai_message, tool_call_id),
                        tool_message,
                    )
                )
            return Command(
                graph=Command.PARENT,
                goto=[Send(agent_name, {**state, "messages": handoff_messages})],
            )
        # Handle single handoff
        else:
            if add_handoff_messages:
                handoff_messages = state["messages"] + [tool_message]
            else:
                handoff_messages = state["messages"][:-1]
            return Command(
                goto=agent_name,
                graph=Command.PARENT,
                # NOTE: this is a state update that will be applied to the swarm multi-agent graph (i.e., the PARENT graph)
                update={
                    **state,
                    "messages": handoff_messages,
                    "active_agent": agent_name,
                    # optionally pass the task description to the next agent
                    # NOTE: individual agents would need to have `task_description` in their state schema
                    # and would need to implement logic for how to consume it
                    "task_description": task_description,
                },
            )

    handoff_to_agent.metadata = {METADATA_KEY_HANDOFF_DESTINATION: agent_name}
    return handoff_to_agent
