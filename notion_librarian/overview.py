import os
from notion_client import Client
from langgraph.prebuilt import create_react_agent
from langchain.tools import tool
from langgraph.types import interrupt, Command
from notion_librarian.llm import model_agent
from notion_librarian.template import get_template_data, create_page_from_template
from notion_librarian.hil import add_human_in_the_loop


DATABASE_ID = "1fdb52b17b34800c9a88eabf06f74470"
TEMPLATE_PAGE_ID = "1feb52b17b34800b8380e08aa58f0414"

notion = Client(auth=os.getenv("NOTION_INTEGRATION_SECRET"))

try:
    with open("notion_librarian/prompts/overview.md", "r", encoding="utf-8") as file:
        prompt = file.read()
except FileNotFoundError:
    print("File not found")
except IOError as e:
    print(f"Error reading file: {e}")


@tool(parse_docstring=True)
def create_project_overview_page(project_name: str) -> str:
    """プロジェクト概要ページを作成する

    Args:
        project_name (str): プロジェクト名

    Returns:
        str: ページID
    """
    props, blocks = get_template_data(TEMPLATE_PAGE_ID)
    created_page = create_page_from_template(
        DATABASE_ID,
        props,
        blocks,
        {"名前": {"title": [{"text": {"content": f"{project_name}【PJ概要資料】"}}]}},
    )
    return created_page["id"]


@tool(parse_docstring=True)
def update_page_content(page_id: str, content: list) -> None:
    """ページのコンテンツを更新する

    Args:
        page_id (str): ページID
        content (list): 更新するコンテンツ

    Returns::
        None
    """
    notion.blocks.children.append(block_id=page_id, children=content)
    return None


@tool(parse_docstring=True)
def update_page_properties(
    page_id: str,
    start_date: str = None,
    end_date: str = None,
    status: str = None,
    assignee: str = None,
    related_division: str = None,
    summary: str = None,
) -> None:
    """特定のページプロパティを更新する

    Args:
        page_id (str): ページID
        start_date (str, optional): 開始日
        end_date (str, optional): 終了日 (開始日より後の日付で指定)
        status (str, optional): ステータス ("未着手" | "進行中" | "完了")
        assignee (str, optional): 担当者 (カンマ区切りで入力)
        related_division (str, optional): 関連事業部 ("全社" | "AI受託" | "教育" | "マーケティング" | "開発" | "営業")
        summary (str, optional): 150字要約

    Returns:
        None
    """
    properties = {}

    if start_date is not None:
        properties["PJ_開始日"] = {"date": {"start": start_date}}

    if end_date is not None:
        properties["PJ_終了日"] = {"date": {"end": end_date}}

    if status is not None:
        properties["PJ_ステータス"] = {"status": {"name": status}}

    if assignee is not None:
        assignees = [name.strip() for name in assignee.split(",")]
        properties["PJ_担当者"] = {
            "multi_select": [{"name": name} for name in assignees]
        }

    if related_division is not None:
        divisions = [div.strip() for div in related_division.split(",")]
        properties["関連事業部"] = {
            "multi_select": [{"name": div} for div in divisions]
        }

    if summary is not None:
        if len(summary) > 150:
            summary = summary[:147] + "..."
        properties["150字要約"] = {"rich_text": [{"text": {"content": summary}}]}

    if properties:
        notion.pages.update(page_id=page_id, properties=properties)

    return None


overview_agent = create_react_agent(
    model=model_agent,
    tools=[
        add_human_in_the_loop(
            create_project_overview_page,
        ),
        add_human_in_the_loop(
            update_page_properties,
        ),
        # add_human_in_the_loop(
        #     update_page_content,
        # ),
    ],
    prompt=prompt,
    name="overview_agent",
)
