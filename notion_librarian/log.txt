{'object': 'list', 'results': [{'object': 'page', 'id': '205b52b1-7b34-80c3-bc7f-c1ed6804d522', 'created_time': '2025-06-01T05:38:00.000Z', 'last_edited_time': '2025-06-01T06:03:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '9021f8ff-2e6b-473d-a9eb-823afdcfe255', 'name': '議事録', 'color': 'green'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-06-01T06:03:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'この議事録は、2025年6月1日に行われた開発定例会議の内容を記録しています。議題にはPJ進捗報告やスプリント計画、技術の共有（n8n、claude code action、テスト駆動開発）が含まれています。現在のPJステータスは未着手で、作成者は三橋直史です。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'この議事録は、2025年6月1日に行われた開発定例会議の内容を記録しています。議題にはPJ進捗報告やスプリント計画、技術の共有（n8n、claude code action、テスト駆動開発）が含まれています。現在のPJステータスは未着手で、作成者は三橋直史です。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-06-01T05:38:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '20250601開発定例【議事録】 ', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '20250601開発定例【議事録】 ', 'href': None}]}}, 'url': 'https://www.notion.so/20250601-205b52b17b3480c3bc7fc1ed6804d522', 'public_url': None}, {'object': 'page', 'id': '204b52b1-7b34-80d1-9be1-e602e0f5d769', 'created_time': '2025-05-31T16:00:00.000Z', 'last_edited_time': '2025-06-01T06:02:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-06-01T06:02:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページではテスト駆動開発（TDD）について説明しています。FizzBuzz問題を通じて、テストの重要性や設計手法を検討し、テストの容易性と効果を考慮したコーディングの進め方を示しています。また、仮実装やリファクタリングのプロセスについても触れ、実際のテストコードを書く際のポイントをまとめています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページではテスト駆動開発（TDD）について説明しています。FizzBuzz問題を通じて、テストの重要性や設計手法を検討し、テストの容易性と効果を考慮したコーディングの進め方を示しています。また、仮実装やリファクタリングのプロセスについても触れ、実際のテストコードを書く際のポイントをまとめています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-31T16:00:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'テスト駆動開発（TDD）', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'テスト駆動開発（TDD）', 'href': None}]}}, 'url': 'https://www.notion.so/TDD-204b52b17b3480d19be1e602e0f5d769', 'public_url': None}, {'object': 'page', 'id': '203b52b1-7b34-807f-8ee0-d2524f2d0607', 'created_time': '2025-05-30T20:08:00.000Z', 'last_edited_time': '2025-05-30T20:19:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-30T20:19:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このドキュメントは、システム開発の流れを要件定義からリリースまでの各ステップに分けて説明しています。各ステップで考慮すべき要点やアウトプット例を示し、アジャイル手法の適用における深さとリスク管理の重要性を強調しています。また、具体的なサンプル開発例も含まれています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このドキュメントは、システム開発の流れを要件定義からリリースまでの各ステップに分けて説明しています。各ステップで考慮すべき要点やアウトプット例を示し、アジャイル手法の適用における深さとリスク管理の重要性を強調しています。また、具体的なサンプル開発例も含まれています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-30T20:08:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'システム開発の流れ', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'システム開発の流れ', 'href': None}]}}, 'url': 'https://www.notion.so/203b52b17b34807f8ee0d2524f2d0607', 'public_url': None}, {'object': 'page', 'id': '203b52b1-7b34-8011-b028-eb45d5c2ae28', 'created_time': '2025-05-30T11:06:00.000Z', 'last_edited_time': '2025-05-30T15:31:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '80c23274-841a-45d3-a756-66791380e446', 'name': 'PJ概要資料', 'color': 'default'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-30T15:31:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'この文書は「校正AIプロジェクト」の概要資料であり、プロジェクトの目的、背景、成功指標、利害関係者、チーム構成、リスク、コミュニケーション方針が含まれています。また、スプリント計画や搭載機能に関する詳細も記載されています。プロジェクトは未着手で、今後のスケジュールや具体的なタスクが計画されています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'この文書は「校正AIプロジェクト」の概要資料であり、プロジェクトの目的、背景、成功指標、利害関係者、チーム構成、リスク、コミュニケーション方針が含まれています。また、スプリント計画や搭載機能に関する詳細も記載されています。プロジェクトは未着手で、今後のスケジュールや具体的なタスクが計画されています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-30T11:06:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '校正AI【PJ概要資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '校正AI【PJ概要資料】', 'href': None}]}}, 'url': 'https://www.notion.so/AI-PJ-203b52b17b348011b028eb45d5c2ae28', 'public_url': None}, {'object': 'page', 'id': '203b52b1-7b34-80cf-a292-f3e640b1699a', 'created_time': '2025-05-30T10:27:00.000Z', 'last_edited_time': '2025-05-30T10:33:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-30T10:33:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'OAuth認証は、他のサービスに代わりに認証を行わせる仕組みで、パスワードを共有せずに安全に情報を管理できます。トークンを用いて権限を細かく設定でき、いつでも取り消すことが可能です。認証の種類やメリット・デメリットについても説明されています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'OAuth認証は、他のサービスに代わりに認証を行わせる仕組みで、パスワードを共有せずに安全に情報を管理できます。トークンを用いて権限を細かく設定でき、いつでも取り消すことが可能です。認証の種類やメリット・デメリットについても説明されています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-30T10:27:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'Oauth認証とは', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Oauth認証とは', 'href': None}]}}, 'url': 'https://www.notion.so/Oauth-203b52b17b3480cfa292f3e640b1699a', 'public_url': None}, {'object': 'page', 'id': '202b52b1-7b34-80e2-94da-f7869cc79abd', 'created_time': '2025-05-29T11:22:00.000Z', 'last_edited_time': '2025-05-29T11:35:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '1f1bacd5-3362-4073-8cf3-c0bb9b363cfe'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '24a104a2-8f9a-4f89-aa7d-0672ec0d8deb', 'name': 'PJ補足資料', 'color': 'yellow'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-29T11:35:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '202b52b1-7b34-80c1-8100-c3fc9426fb97'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは人材事業に関する集客施策の補足資料で、PJのステータスは未着手です。リファラルマーケティングに関連する情報が含まれており、作成者は三橋直史、最終更新者は富島拓海です。更新日は2025年5月29日です。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは人材事業に関する集客施策の補足資料で、PJのステータスは未着手です。リファラルマーケティングに関連する情報が含まれており、作成者は三橋直史、最終更新者は富島拓海です。更新日は2025年5月29日です。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-29T11:22:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '1f1bacd5-3362-4073-8cf3-c0bb9b363cfe', 'name': '富島拓海', 'avatar_url': 'https://lh3.googleusercontent.com/a/ACg8ocLSc-kLYZSOha4okpXpcMMsUHSKZs0NhEUCRKhZsHLEIpLpdA=s100', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '人材事業集客施策【PJ補足資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '人材事業集客施策【PJ補足資料】', 'href': None}]}}, 'url': 'https://www.notion.so/PJ-202b52b17b3480e294daf7869cc79abd', 'public_url': None}, {'object': 'page', 'id': '202b52b1-7b34-80c1-8100-c3fc9426fb97', 'created_time': '2025-05-29T11:16:00.000Z', 'last_edited_time': '2025-05-29T11:18:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '80c23274-841a-45d3-a756-66791380e446', 'name': 'PJ概要資料', 'color': 'default'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': '9bb79354-2044-46c1-be18-083145874095', 'name': '進行中', 'color': 'blue'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-29T11:18:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': [{'object': 'user', 'id': '1f1bacd5-3362-4073-8cf3-c0bb9b363cfe', 'name': '富島拓海', 'avatar_url': 'https://lh3.googleusercontent.com/a/ACg8ocLSc-kLYZSOha4okpXpcMMsUHSKZs0NhEUCRKhZsHLEIpLpdA=s100', 'type': 'person', 'person': {'email': '<EMAIL>'}}, {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': {'start': '2025-05-29', 'end': '2025-08-30', 'time_zone': None}}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '202b52b1-7b34-80e2-94da-f7869cc79abd'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、「人材イベント」のプロジェクト概要資料で、プロジェクトの目的、背景、成功指標、利害関係者、チーム構成、リスク管理などが記されています。プロジェクトは進行中で、2025年5月29日から2025年8月30日まで実施される予定です。スプリント計画やタスクの詳細も含まれています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、「人材イベント」のプロジェクト概要資料で、プロジェクトの目的、背景、成功指標、利害関係者、チーム構成、リスク管理などが記されています。プロジェクトは進行中で、2025年5月29日から2025年8月30日まで実施される予定です。スプリント計画やタスクの詳細も含まれています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-29T11:16:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': 'ff21eab6-b1ca-4052-8fca-18c419b54f37', 'name': '新規事業', 'color': 'gray'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '人材イベント【PJ概要資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '人材イベント【PJ概要資料】', 'href': None}]}}, 'url': 'https://www.notion.so/PJ-202b52b17b3480c18100c3fc9426fb97', 'public_url': None}, {'object': 'page', 'id': '202b52b1-7b34-80a5-b6b6-ea72a061e2e4', 'created_time': '2025-05-29T03:18:00.000Z', 'last_edited_time': '2025-05-29T16:51:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-29T16:51:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'n8nは、複数のアプリを統合するためのツールであり、複雑な処理が不要な場合に便利です。このページでは、n8nの基本機能や活用例について説明されており、具体的な使い方が提案されています。また、関連するリソースへのリンクも含まれています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'n8nは、複数のアプリを統合するためのツールであり、複雑な処理が不要な場合に便利です。このページでは、n8nの基本機能や活用例について説明されており、具体的な使い方が提案されています。また、関連するリソースへのリンクも含まれています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-29T03:18:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'n8n概要', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'n8n概要', 'href': None}]}}, 'url': 'https://www.notion.so/n8n-202b52b17b3480a5b6b6ea72a061e2e4', 'public_url': None}, {'object': 'page', 'id': '201b52b1-7b34-8069-99b8-dad26465d24d', 'created_time': '2025-05-28T13:33:00.000Z', 'last_edited_time': '2025-05-29T16:05:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-29T16:05:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'LangGraphはAIエージェントの開発と実装を支援するフレームワークで、ユーザーのニーズに応じたツール呼び出し、メモリ管理、プランニング機能を提供します。これにより、複雑なワークフローを簡素化し、高度なタスク特化型エージェントを構築できます。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'LangGraphはAIエージェントの開発と実装を支援するフレームワークで、ユーザーのニーズに応じたツール呼び出し、メモリ管理、プランニング機能を提供します。これにより、複雑なワークフローを簡素化し、高度なタスク特化型エージェントを構築できます。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-28T13:33:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': '5db28dda-cb8a-4d43-bd9d-a111c9f42c15', 'name': '開発', 'color': 'blue'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'LangGraph概要', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'LangGraph概要', 'href': None}]}}, 'url': 'https://www.notion.so/LangGraph-201b52b17b34806999b8dad26465d24d', 'public_url': None}, {'object': 'page', 'id': '200b52b1-7b34-800e-b322-ffc8dc7b5f2b', 'created_time': '2025-05-27T10:04:00.000Z', 'last_edited_time': '2025-05-27T14:18:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-27T14:18:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'Human-in-the-loop（HIL）は、自動化されたプロセスに人間の介入を可能にする仕組みです。特に大規模言語モデル（LLM）を用いたアプリケーションにおいて、出力の検証や修正、追加コンテキストの提供が求められます。主な機能には永続的な実行状態の保存と柔軟な統合ポイントの導入が含まれ、一般的なユースケースとしてツール呼び出しのレビューやLLM出力の検証が挙げられます。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Human-in-the-loop（HIL）は、自動化されたプロセスに人間の介入を可能にする仕組みです。特に大規模言語モデル（LLM）を用いたアプリケーションにおいて、出力の検証や修正、追加コンテキストの提供が求められます。主な機能には永続的な実行状態の保存と柔軟な統合ポイントの導入が含まれ、一般的なユースケースとしてツール呼び出しのレビューやLLM出力の検証が挙げられます。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-27T10:04:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': '5db28dda-cb8a-4d43-bd9d-a111c9f42c15', 'name': '開発', 'color': 'blue'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'Human in the loopの導入', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Human in the loopの導入', 'href': None}]}}, 'url': 'https://www.notion.so/Human-in-the-loop-200b52b17b34800eb322ffc8dc7b5f2b', 'public_url': None}, {'object': 'page', 'id': '200b52b1-7b34-8023-814b-c756fee00406', 'created_time': '2025-05-27T09:50:00.000Z', 'last_edited_time': '2025-05-27T10:03:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-27T10:03:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、MCP（マルチクラウドプラットフォーム）の導入に関するもので、目的はclaude codeにMCPを統合し、AIエージェントからMCPを呼び出すことです。関連情報として、更新日や作成者、関連事業部などのメタデータも含まれています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、MCP（マルチクラウドプラットフォーム）の導入に関するもので、目的はclaude codeにMCPを統合し、AIエージェントからMCPを呼び出すことです。関連情報として、更新日や作成者、関連事業部などのメタデータも含まれています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-27T09:50:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': '5db28dda-cb8a-4d43-bd9d-a111c9f42c15', 'name': '開発', 'color': 'blue'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'MCPの導入', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'MCPの導入', 'href': None}]}}, 'url': 'https://www.notion.so/MCP-200b52b17b348023814bc756fee00406', 'public_url': None}, {'object': 'page', 'id': '200b52b1-7b34-8061-abb0-e0d4b9d5cc7a', 'created_time': '2025-05-27T08:56:00.000Z', 'last_edited_time': '2025-05-29T09:06:00.000Z', 'created_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42'}, 'last_edited_by': {'object': 'user', 'id': '1d5d872b-594c-81cf-8958-0002db42f273'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '80c23274-841a-45d3-a756-66791380e446', 'name': 'PJ概要資料', 'color': 'default'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': '9bb79354-2044-46c1-be18-083145874095', 'name': '進行中', 'color': 'blue'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-29T09:06:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': [{'object': 'user', 'id': 'fde7c4bc-b8bb-432f-a43c-928b89587fd4', 'name': 'KYOUNGPIN GWAK', 'avatar_url': 'https://lh3.googleusercontent.com/a/ACg8ocJTbqIGDOxCNROHqlZ2DjSeG9KzVf0RwZsVjx0PHP47=s100', 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': {'start': '2025-05-20', 'end': None, 'time_zone': None}}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このPJ概要資料では、ミーティングAIの目的や課題、成功指標、チーム構成などが記載されています。AIは会議中に不足する情報を調査し表示することで、会議の回数を減らすことを目指しています。また、オンラインミーティングに特化し、関係者全体に影響を与えるプロジェクトです。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このPJ概要資料では、ミーティングAIの目的や課題、成功指標、チーム構成などが記載されています。AIは会議中に不足する情報を調査し表示することで、会議の回数を減らすことを目指しています。また、オンラインミーティングに特化し、関係者全体に影響を与えるプロジェクトです。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-27T08:56:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': [{'object': 'user', 'id': 'fde7c4bc-b8bb-432f-a43c-928b89587fd4', 'name': 'KYOUNGPIN GWAK', 'avatar_url': 'https://lh3.googleusercontent.com/a/ACg8ocJTbqIGDOxCNROHqlZ2DjSeG9KzVf0RwZsVjx0PHP47=s100', 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': 'e10d1fb7-e698-461a-9930-39298fc23aaa', 'name': '全社', 'color': 'brown'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '1d5d872b-594c-81cf-8958-0002db42f273', 'name': 'Taira Urakawa', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/2128c39b-ce3b-4843-9ff1-197e57038b9c/%E3%82%A2%E3%83%BC%E3%83%88%E3%83%9C%E3%83%BC%E3%83%89_1.webp', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42', 'name': '常安虹', 'avatar_url': None, 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'ミーティングAI【PJ概要資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'ミーティングAI【PJ概要資料】', 'href': None}]}}, 'url': 'https://www.notion.so/AI-PJ-200b52b17b348061abb0e0d4b9d5cc7a', 'public_url': None}, {'object': 'page', 'id': '200b52b1-7b34-8030-a3ec-dad6667086df', 'created_time': '2025-05-27T08:42:00.000Z', 'last_edited_time': '2025-05-28T09:32:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '1f1bacd5-3362-4073-8cf3-c0bb9b363cfe'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-28T09:32:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': '富島くんの技術力育成日記では、営業型PMとしての理想像や新規事業開発に必要なスキルが述べられています。技術リテラシーを持ち、エンジニアと同じ視点で話せる人材が求められ、ビジネス課題を技術要素に翻訳する能力が重視されています。プロジェクト速度向上のため、簡単なプロトタイプを作成できるスキルも重要です。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '富島くんの技術力育成日記では、営業型PMとしての理想像や新規事業開発に必要なスキルが述べられています。技術リテラシーを持ち、エンジニアと同じ視点で話せる人材が求められ、ビジネス課題を技術要素に翻訳する能力が重視されています。プロジェクト速度向上のため、簡単なプロトタイプを作成できるスキルも重要です。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-27T08:42:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': '5db28dda-cb8a-4d43-bd9d-a111c9f42c15', 'name': '開発', 'color': 'blue'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '1f1bacd5-3362-4073-8cf3-c0bb9b363cfe', 'name': '富島拓海', 'avatar_url': 'https://lh3.googleusercontent.com/a/ACg8ocLSc-kLYZSOha4okpXpcMMsUHSKZs0NhEUCRKhZsHLEIpLpdA=s100', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '富島くん_技術力育成日記', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '富島くん_技術力育成日記', 'href': None}]}}, 'url': 'https://www.notion.so/_-200b52b17b348030a3ecdad6667086df', 'public_url': None}, {'object': 'page', 'id': '200b52b1-7b34-80e7-871e-ed29326b89db', 'created_time': '2025-05-27T08:10:00.000Z', 'last_edited_time': '2025-06-01T05:55:00.000Z', 'created_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42'}, 'last_edited_by': {'object': 'user', 'id': '1d5d872b-594c-81cf-8958-0002db42f273'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '80c23274-841a-45d3-a756-66791380e446', 'name': 'PJ概要資料', 'color': 'default'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': '9bb79354-2044-46c1-be18-083145874095', 'name': '進行中', 'color': 'blue'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-06-01T05:55:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': [{'object': 'user', 'id': '20d79b80-7da0-40db-be02-f8f546a50624', 'name': '山盛陽平', 'avatar_url': 'https://lh3.googleusercontent.com/a/AGNmyxZQTs9HEdmaLg95ZwUMoLJO5Q81cWY31qaozrV2=s100', 'type': 'person', 'person': {'email': '<EMAIL>'}}, {'object': 'user', 'id': '1d5d872b-594c-81cf-8958-0002db42f273', 'name': 'Taira Urakawa', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/2128c39b-ce3b-4843-9ff1-197e57038b9c/%E3%82%A2%E3%83%BC%E3%83%88%E3%83%9C%E3%83%BC%E3%83%89_1.webp', 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': {'start': '2025-05-20', 'end': None, 'time_zone': None}}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1fdb52b1-7b34-80e2-a0de-e1e82263c339'}, {'id': '1fdb52b1-7b34-80ee-b3a2-ce404982c8f6'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このプロジェクト概要資料は、Notionを活用したAI情報取得の効率化を目的とし、関連タスクや成功指標、リスク、チーム構成などを記載しています。スケジュールやスプリント計画も含まれており、具体的な実装タスクが示されています。プロジェクトは進行中で、担当者は山盛陽平とTaira Urakawaです。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このプロジェクト概要資料は、Notionを活用したAI情報取得の効率化を目的とし、関連タスクや成功指標、リスク、チーム構成などを記載しています。スケジュールやスプリント計画も含まれており、具体的な実装タスクが示されています。プロジェクトは進行中で、担当者は山盛陽平とTaira Urakawaです。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-27T08:10:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': [{'object': 'user', 'id': '1d5d872b-594c-81cf-8958-0002db42f273', 'name': 'Taira Urakawa', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/2128c39b-ce3b-4843-9ff1-197e57038b9c/%E3%82%A2%E3%83%BC%E3%83%88%E3%83%9C%E3%83%BC%E3%83%89_1.webp', 'type': 'person', 'person': {'email': '<EMAIL>'}}, {'object': 'user', 'id': '20d79b80-7da0-40db-be02-f8f546a50624', 'name': '山盛陽平', 'avatar_url': 'https://lh3.googleusercontent.com/a/AGNmyxZQTs9HEdmaLg95ZwUMoLJO5Q81cWY31qaozrV2=s100', 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': 'e10d1fb7-e698-461a-9930-39298fc23aaa', 'name': '全社', 'color': 'brown'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '1d5d872b-594c-81cf-8958-0002db42f273', 'name': 'Taira Urakawa', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/2128c39b-ce3b-4843-9ff1-197e57038b9c/%E3%82%A2%E3%83%BC%E3%83%88%E3%83%9C%E3%83%BC%E3%83%89_1.webp', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42', 'name': '常安虹', 'avatar_url': None, 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'Notion管理PJ【PJ概要資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Notion管理PJ【PJ概要資料】', 'href': None}]}}, 'url': 'https://www.notion.so/Notion-PJ-PJ-200b52b17b3480e7871eed29326b89db', 'public_url': None}, {'object': 'page', 'id': '200b52b1-7b34-803c-a4e4-f839f0ae7059', 'created_time': '2025-05-27T08:03:00.000Z', 'last_edited_time': '2025-05-27T08:48:00.000Z', 'created_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42'}, 'last_edited_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': 'b86ab48f-42aa-47fd-a84a-17f1c08b2f38', 'name': 'タスク', 'color': 'orange'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-27T08:48:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': [{'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42', 'name': '常安虹', 'avatar_url': None, 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': {'start': '2025-05-27', 'end': '2025-06-03', 'time_zone': None}}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1feb52b1-7b34-80f6-ac23-ed249e40883b'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'この文書は、ReactとSpectacleライブラリを使用したLLM対応のスライドコンポーネントの開発に関するガイドです。Tailwind CSSとshadcn/uiを統合し、再利用可能なスライドコンポーネントを構築する方法、及びそれに必要な技術や設定について詳しく説明しています。また、コンポーネント駆動アプローチのメリットや、LLMによるプレゼンテーション生成の柔軟性についても触れています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'この文書は、ReactとSpectacleライブラリを使用したLLM対応のスライドコンポーネントの開発に関するガイドです。Tailwind CSSとshadcn/uiを統合し、再利用可能なスライドコンポーネントを構築する方法、及びそれに必要な技術や設定について詳しく説明しています。また、コンポーネント駆動アプローチのメリットや、LLMによるプレゼンテーション生成の柔軟性についても触れています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-27T08:03:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': [{'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42', 'name': '常安虹', 'avatar_url': None, 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': {'id': '9c9f8e3a-371f-4549-a249-093581abb4c9', 'name': '完了', 'color': 'gray'}}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': 'e10d1fb7-e698-461a-9930-39298fc23aaa', 'name': '全社', 'color': 'brown'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42', 'name': '常安虹', 'avatar_url': None, 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42', 'name': '常安虹', 'avatar_url': None, 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'spectacleリサーチ【タスク】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'spectacleリサーチ【タスク】', 'href': None}]}}, 'url': 'https://www.notion.so/spectacle-200b52b17b34803ca4e4f839f0ae7059', 'public_url': None}, {'object': 'page', 'id': '1ffb52b1-7b34-8063-8d3a-c2834256c926', 'created_time': '2025-05-26T14:54:00.000Z', 'last_edited_time': '2025-06-01T05:59:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '7b79dd41-f77e-46a0-9485-0e7d07614019', 'name': 'ナレッジ共有', 'color': 'pink'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-06-01T05:59:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、Claude Codeの導入に関するプロジェクト計画を示しており、背景や要件定義、プロンプトの詳細が記載されています。また、Claude Codeの開発に関するヒントや効率的なワークフローの構築方法も説明されています。最終的には、AIを活用したプログラミングの精度向上を目指しています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、Claude Codeの導入に関するプロジェクト計画を示しており、背景や要件定義、プロンプトの詳細が記載されています。また、Claude Codeの開発に関するヒントや効率的なワークフローの構築方法も説明されています。最終的には、AIを活用したプログラミングの精度向上を目指しています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-26T14:54:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': '5db28dda-cb8a-4d43-bd9d-a111c9f42c15', 'name': '開発', 'color': 'blue'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'Claude codeの導入', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Claude codeの導入', 'href': None}]}}, 'url': 'https://www.notion.so/Claude-code-1ffb52b17b3480638d3ac2834256c926', 'public_url': None}, {'object': 'page', 'id': '1ffb52b1-7b34-8053-93de-cd9f4fbaf3e1', 'created_time': '2025-05-26T11:58:00.000Z', 'last_edited_time': '2025-05-26T11:59:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '1857b74c-5163-4049-ba20-7abb5ee1970c', 'name': 'アイデア', 'color': 'gray'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-26T11:59:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、AIを利用して授業動画を自動生成するアイデアに関するもので、プロジェクトは未着手です。関連事業部は教育で、最終更新者は三橋直史氏です。作成日は2025年5月26日で、ページの内容は教育現場での効率的な動画作成を目指しています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、AIを利用して授業動画を自動生成するアイデアに関するもので、プロジェクトは未着手です。関連事業部は教育で、最終更新者は三橋直史氏です。作成日は2025年5月26日で、ページの内容は教育現場での効率的な動画作成を目指しています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-26T11:58:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': '9f9df830-6825-414e-bdff-fd4a0a3bfa98', 'name': '教育', 'color': 'yellow'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'AIによる授業動画自動生成', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'AIによる授業動画自動生成', 'href': None}]}}, 'url': 'https://www.notion.so/AI-1ffb52b17b34805393decd9f4fbaf3e1', 'public_url': None}, {'object': 'page', 'id': '1ffb52b1-7b34-80a2-a51a-e922277f4356', 'created_time': '2025-05-26T11:33:00.000Z', 'last_edited_time': '2025-05-26T11:35:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '9021f8ff-2e6b-473d-a9eb-823afdcfe255', 'name': '議事録', 'color': 'green'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-26T11:35:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'この議事録は、2025年5月26日に行われた経営MTGの内容を記録したもので、PJステータスは未着手です。議題にはNHKビジクリ提案整理と人材事業整理が含まれています。最終更新者と作成者は三橋直史です。今後の進行状況についてのネクストや決定事項は未記入です。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'この議事録は、2025年5月26日に行われた経営MTGの内容を記録したもので、PJステータスは未着手です。議題にはNHKビジクリ提案整理と人材事業整理が含まれています。最終更新者と作成者は三橋直史です。今後の進行状況についてのネクストや決定事項は未記入です。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-26T11:33:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '20250527_経営MTG【議事録】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '20250527_経営MTG【議事録】', 'href': None}]}}, 'url': 'https://www.notion.so/20250527_-MTG-1ffb52b17b3480a2a51ae922277f4356', 'public_url': None}, {'object': 'page', 'id': '1ffb52b1-7b34-80f4-a760-f827a9096b3e', 'created_time': '2025-05-26T07:07:00.000Z', 'last_edited_time': '2025-05-26T08:29:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '1857b74c-5163-4049-ba20-7abb5ee1970c', 'name': 'アイデア', 'color': 'gray'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-26T08:29:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページでは、MTGの発行と議事録作成の効率化について説明しています。管理画面から会議を作成し、GoogleカレンダーやMeetを利用する方法が示されており、具体的なPythonコードが提供されています。会議の要約や参加者の設定も含まれています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページでは、MTGの発行と議事録作成の効率化について説明しています。管理画面から会議を作成し、GoogleカレンダーやMeetを利用する方法が示されており、具体的なPythonコードが提供されています。会議の要約や参加者の設定も含まれています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-26T07:07:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [{'id': '1fdb52b1-7b34-80d1-833f-c97aaf895c49'}], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'MTG発行・議事録作成の効率化', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'MTG発行・議事録作成の効率化', 'href': None}]}}, 'url': 'https://www.notion.so/MTG-1ffb52b17b3480f4a760f827a9096b3e', 'public_url': None}, {'object': 'page', 'id': '1ffb52b1-7b34-80ef-befa-d013ae1db6b7', 'created_time': '2025-05-26T02:01:00.000Z', 'last_edited_time': '2025-05-26T05:57:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '24a104a2-8f9a-4f89-aa7d-0672ec0d8deb', 'name': 'PJ補足資料', 'color': 'yellow'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-26T05:57:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1ffb52b1-7b34-800f-bc7f-f02244e5ea87'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、LP作成に関するプログラミングのPJ補足資料を含んでいます。現在のPJステータスは未着手で、更新日や作成者情報が記載されています。また、宿題としてHTML、CSS、Tailwindの勉強や環境構築が挙げられ、Webやプログラミングについての説明が含まれています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、LP作成に関するプログラミングのPJ補足資料を含んでいます。現在のPJステータスは未着手で、更新日や作成者情報が記載されています。また、宿題としてHTML、CSS、Tailwindの勉強や環境構築が挙げられ、Webやプログラミングについての説明が含まれています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-26T02:01:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'LP作成のためのプログラムミング【PJ補足資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'LP作成のためのプログラムミング【PJ補足資料】', 'href': None}]}}, 'url': 'https://www.notion.so/LP-PJ-1ffb52b17b3480efbefad013ae1db6b7', 'public_url': None}, {'object': 'page', 'id': '1ffb52b1-7b34-800f-bc7f-f02244e5ea87', 'created_time': '2025-05-26T02:00:00.000Z', 'last_edited_time': '2025-05-26T02:02:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '80c23274-841a-45d3-a756-66791380e446', 'name': 'PJ概要資料', 'color': 'default'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-26T02:02:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': [{'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': {'start': '2025-05-26', 'end': '2025-06-10', 'time_zone': None}}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1ffb52b1-7b34-80ef-befa-d013ae1db6b7'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このPJ概要資料は、LPの量産体制確立に向けたプロジェクトの目的や課題、成功指標を定義しています。プロジェクトは未着手で、2025年5月26日から6月10日までの期間で、2時間で1LPを作成することを目指しています。コミュニケーション方針やリスク管理についても記載されています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このPJ概要資料は、LPの量産体制確立に向けたプロジェクトの目的や課題、成功指標を定義しています。プロジェクトは未着手で、2025年5月26日から6月10日までの期間で、2時間で1LPを作成することを目指しています。コミュニケーション方針やリスク管理についても記載されています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-26T02:00:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'LPの量産体制確立【PJ概要資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'LPの量産体制確立【PJ概要資料】', 'href': None}]}}, 'url': 'https://www.notion.so/LP-PJ-1ffb52b17b34800fbc7ff02244e5ea87', 'public_url': None}, {'object': 'page', 'id': '1feb52b1-7b34-80f6-ac23-ed249e40883b', 'created_time': '2025-05-25T08:13:00.000Z', 'last_edited_time': '2025-06-01T05:51:00.000Z', 'created_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '80c23274-841a-45d3-a756-66791380e446', 'name': 'PJ概要資料', 'color': 'default'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': '9bb79354-2044-46c1-be18-083145874095', 'name': '進行中', 'color': 'blue'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-06-01T05:51:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': [{'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42', 'name': '常安虹', 'avatar_url': None, 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': {'start': '2025-05-20', 'end': None, 'time_zone': None}}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '200b52b1-7b34-803c-a4e4-f839f0ae7059'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'この文書は、スライドの自動生成に関するプロジェクト概要を示しています。目的は、会議の議事録やリサーチ対象を入力することでスライドを迅速に作成し、後からカスタマイズできる仕組みを構築することです。成功指標やチーム構成、技術スタックなども詳述されており、開発プロセスやリスク管理についても触れています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'この文書は、スライドの自動生成に関するプロジェクト概要を示しています。目的は、会議の議事録やリサーチ対象を入力することでスライドを迅速に作成し、後からカスタマイズできる仕組みを構築することです。成功指標やチーム構成、技術スタックなども詳述されており、開発プロセスやリスク管理についても触れています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-25T08:13:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': 'e10d1fb7-e698-461a-9930-39298fc23aaa', 'name': '全社', 'color': 'brown'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '191d872b-594c-818a-8700-00028ff1cb42', 'name': '常安虹', 'avatar_url': None, 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'スライドの自動生成【PJ概要資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'スライドの自動生成【PJ概要資料】', 'href': None}]}}, 'url': 'https://www.notion.so/PJ-1feb52b17b3480f6ac23ed249e40883b', 'public_url': None}, {'object': 'page', 'id': '1feb52b1-7b34-8049-beb1-fab3f148c5a1', 'created_time': '2025-05-25T07:46:00.000Z', 'last_edited_time': '2025-05-25T07:53:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '1857b74c-5163-4049-ba20-7abb5ee1970c', 'name': 'アイデア', 'color': 'gray'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-25T07:53:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'Deep Researchの調査は、構造を理解し、社内ドキュメントの検索や知識共有を効率化することを目的としています。重要な観点として、検索アルゴリズム、ドキュメントの構造化、セキュリティ管理、他ツールとの連携が挙げられます。実務に即した活用シーンを考慮しながら進めることが求められています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Deep Researchの調査は、構造を理解し、社内ドキュメントの検索や知識共有を効率化することを目的としています。重要な観点として、検索アルゴリズム、ドキュメントの構造化、セキュリティ管理、他ツールとの連携が挙げられます。実務に即した活用シーンを考慮しながら進めることが求められています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-25T07:46:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'Deep Researchの調査', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Deep Researchの調査', 'href': None}]}}, 'url': 'https://www.notion.so/Deep-Research-1feb52b17b348049beb1fab3f148c5a1', 'public_url': None}, {'object': 'page', 'id': '1feb52b1-7b34-8059-9873-e6706ddb40b3', 'created_time': '2025-05-25T07:46:00.000Z', 'last_edited_time': '2025-05-25T07:48:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '1857b74c-5163-4049-ba20-7abb5ee1970c', 'name': 'アイデア', 'color': 'gray'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-25T07:48:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、GitHubのレポジトリを把握するためのシステムについて説明しています。目的は技術の進歩に追いつくことで、手段としてdeep wikiの調査が挙げられています。プロジェクトは未着手で、作成者と最終更新者は三橋直史です。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、GitHubのレポジトリを把握するためのシステムについて説明しています。目的は技術の進歩に追いつくことで、手段としてdeep wikiの調査が挙げられています。プロジェクトは未着手で、作成者と最終更新者は三橋直史です。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-25T07:46:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'githubのレポジトリを簡単に把握できるシステム', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'githubのレポジトリを簡単に把握できるシステム', 'href': None}]}}, 'url': 'https://www.notion.so/github-1feb52b17b3480599873e6706ddb40b3', 'public_url': None}, {'object': 'page', 'id': '1feb52b1-7b34-8016-b30f-e6dff63c242e', 'created_time': '2025-05-25T05:43:00.000Z', 'last_edited_time': '2025-05-25T08:22:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '9021f8ff-2e6b-473d-a9eb-823afdcfe255', 'name': '議事録', 'color': 'green'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-25T08:22:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'この議事録は2025年5月25日に開催された開発定例の内容を記録しています。議題にはNotionデータ構造、PJ進捗報告、スプリント計画、技術の共有、その他報告が含まれています。一部の議題は完了しており、gemeniの議事録作成も報告されています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'この議事録は2025年5月25日に開催された開発定例の内容を記録しています。議題にはNotionデータ構造、PJ進捗報告、スプリント計画、技術の共有、その他報告が含まれています。一部の議題は完了しており、gemeniの議事録作成も報告されています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-25T05:43:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '20250525開発定例【議事録】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '20250525開発定例【議事録】', 'href': None}]}}, 'url': 'https://www.notion.so/20250525-1feb52b17b348016b30fe6dff63c242e', 'public_url': None}, {'object': 'page', 'id': '1fdb52b1-7b34-8076-85fe-d1ccb7e73d0c', 'created_time': '2025-05-24T12:38:00.000Z', 'last_edited_time': '2025-05-25T03:11:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '24a104a2-8f9a-4f89-aa7d-0672ec0d8deb', 'name': 'PJ補足資料', 'color': 'yellow'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-25T03:11:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1fdb52b1-7b34-8007-beb2-cdd657009180'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、学習塾における理想的なデータ蓄積体制に関するプロジェクトの補足資料です。プロジェクトは未着手で、作成者は三橋直史です。更新日は2025年5月25日で、アーカイブはされていません。データ管理の重要性や効率的な情報収集の方法についての検討が期待されています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、学習塾における理想的なデータ蓄積体制に関するプロジェクトの補足資料です。プロジェクトは未着手で、作成者は三橋直史です。更新日は2025年5月25日で、アーカイブはされていません。データ管理の重要性や効率的な情報収集の方法についての検討が期待されています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-24T12:38:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '学習塾における理想的なデータ蓄積体制【PJ補足資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '学習塾における理想的なデータ蓄積体制【PJ補足資料】', 'href': None}]}}, 'url': 'https://www.notion.so/PJ-1fdb52b17b34807685fed1ccb7e73d0c', 'public_url': None}, {'object': 'page', 'id': '1fdb52b1-7b34-8007-beb2-cdd657009180', 'created_time': '2025-05-24T12:27:00.000Z', 'last_edited_time': '2025-05-25T12:17:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '80c23274-841a-45d3-a756-66791380e446', 'name': 'PJ概要資料', 'color': 'default'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': '9bb79354-2044-46c1-be18-083145874095', 'name': '進行中', 'color': 'blue'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-25T12:17:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': [{'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}]}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': {'start': '2025-05-24', 'end': None, 'time_zone': None}}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1fdb52b1-7b34-8076-85fe-d1ccb7e73d0c'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'エドバンスアカデミーの内部システム整理PJに関する概要資料です。目的はシステムの整理と運用負荷の軽減、付加価値の向上です。背景にはテクノロジー活用の具体化が課題としてあり、成功指標はシステム完成と業務効率化です。スケジュールには機能整理やPoC開発の締切が設定されています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'エドバンスアカデミーの内部システム整理PJに関する概要資料です。目的はシステムの整理と運用負荷の軽減、付加価値の向上です。背景にはテクノロジー活用の具体化が課題としてあり、成功指標はシステム完成と業務効率化です。スケジュールには機能整理やPoC開発の締切が設定されています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-24T12:27:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': [{'id': '9f9df830-6825-414e-bdff-fd4a0a3bfa98', 'name': '教育', 'color': 'yellow'}]}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'エドバンスアカデミー内部システム整理【PJ概要資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'エドバンスアカデミー内部システム整理【PJ概要資料】', 'href': None}]}}, 'url': 'https://www.notion.so/PJ-1fdb52b17b348007beb2cdd657009180', 'public_url': None}, {'object': 'page', 'id': '1fdb52b1-7b34-80d1-833f-c97aaf895c49', 'created_time': '2025-05-24T10:46:00.000Z', 'last_edited_time': '2025-05-24T10:49:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '1857b74c-5163-4049-ba20-7abb5ee1970c', 'name': 'アイデア', 'color': 'gray'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-24T10:49:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このアイデアは、オンライン会議後にサブアカウントのGeminiを利用して自動生成された議事録メールを受信し、そのメールをトリガーにしてZapierを使ってAIによる文字起こしや整形を行い、Notionデータベースに自動で追加するプロセスを提案しています。これにより、議事録作成・共有にかかる人的コストをほぼゼロにできる可能性があります。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このアイデアは、オンライン会議後にサブアカウントのGeminiを利用して自動生成された議事録メールを受信し、そのメールをトリガーにしてZapierを使ってAIによる文字起こしや整形を行い、Notionデータベースに自動で追加するプロセスを提案しています。これにより、議事録作成・共有にかかる人的コストをほぼゼロにできる可能性があります。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-24T10:46:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [{'id': '1ffb52b1-7b34-80f4-a760-f827a9096b3e'}], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'Meetに会社の共通アカウント招待させておいて文字起こしメール起点の自動入力【アイデア】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Meetに会社の共通アカウント招待させておいて文字起こしメール起点の自動入力【アイデア】', 'href': None}]}}, 'url': 'https://www.notion.so/Meet-1fdb52b17b3480d1833fc97aaf895c49', 'public_url': None}, {'object': 'page', 'id': '1fdb52b1-7b34-80fd-adc3-d43784bdd92b', 'created_time': '2025-05-24T10:34:00.000Z', 'last_edited_time': '2025-05-24T10:45:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '9021f8ff-2e6b-473d-a9eb-823afdcfe255', 'name': '議事録', 'color': 'green'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-24T10:45:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'この議事録は2025年5月23日に行われた経営MTGの記録です。議事録にはページの種類やプロジェクトのステータス、作成者と更新者の情報が含まれています。今後のアクションや決定事項、議題、議論の内容も記載されています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'この議事録は2025年5月23日に行われた経営MTGの記録です。議事録にはページの種類やプロジェクトのステータス、作成者と更新者の情報が含まれています。今後のアクションや決定事項、議題、議論の内容も記載されています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-24T10:34:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': '20250523経営MTG【議事録】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': '20250523経営MTG【議事録】', 'href': None}]}}, 'url': 'https://www.notion.so/20250523-MTG-1fdb52b17b3480fdadc3d43784bdd92b', 'public_url': None}, {'object': 'page', 'id': '1fdb52b1-7b34-80e2-a0de-e1e82263c339', 'created_time': '2025-05-24T10:28:00.000Z', 'last_edited_time': '2025-06-01T03:42:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '20d79b80-7da0-40db-be02-f8f546a50624'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '24a104a2-8f9a-4f89-aa7d-0672ec0d8deb', 'name': 'PJ補足資料', 'color': 'yellow'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-06-01T03:42:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1fdb52b1-7b34-80b8-b66d-c5f4c2bf7448'}, {'id': '200b52b1-7b34-80e7-871e-ed29326b89db'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、Notionを活用した情報設計の基本方針とAIとの連携について詳述しています。目指すべき理想の情報設計、アクションプラン、管理画面の役割、具体的な課題とその対策例を示し、企業事例を通じてナレッジ管理の重要性とAI活用の効果を解説しています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、Notionを活用した情報設計の基本方針とAIとの連携について詳述しています。目指すべき理想の情報設計、アクションプラン、管理画面の役割、具体的な課題とその対策例を示し、企業事例を通じてナレッジ管理の重要性とAI活用の効果を解説しています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-24T10:28:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '20d79b80-7da0-40db-be02-f8f546a50624', 'name': '山盛陽平', 'avatar_url': 'https://lh3.googleusercontent.com/a/AGNmyxZQTs9HEdmaLg95ZwUMoLJO5Q81cWY31qaozrV2=s100', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'Notion情報設計の基本方針【PJ補足資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Notion情報設計の基本方針【PJ補足資料】', 'href': None}]}}, 'url': 'https://www.notion.so/Notion-PJ-1fdb52b17b3480e2a0dee1e82263c339', 'public_url': None}, {'object': 'page', 'id': '1fdb52b1-7b34-80ee-b3a2-ce404982c8f6', 'created_time': '2025-05-24T10:22:00.000Z', 'last_edited_time': '2025-05-29T11:15:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '24a104a2-8f9a-4f89-aa7d-0672ec0d8deb', 'name': 'PJ補足資料', 'color': 'yellow'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-29T11:15:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1fdb52b1-7b34-80b8-b66d-c5f4c2bf7448'}, {'id': '200b52b1-7b34-80e7-871e-ed29326b89db'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このページは、NotionDBに保存される情報の整理に関する補足資料です。PJ関連や学び・気づき、事業部などのデータ区分や、それぞれのデータの概要、必要なカラム、ページ構造について詳細に説明しています。目的は、どのような情報が登録されるかを明確にし、データベースの効果的な運用を促進することです。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このページは、NotionDBに保存される情報の整理に関する補足資料です。PJ関連や学び・気づき、事業部などのデータ区分や、それぞれのデータの概要、必要なカラム、ページ構造について詳細に説明しています。目的は、どのような情報が登録されるかを明確にし、データベースの効果的な運用を促進することです。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': False}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-24T10:22:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'NotionDBに保存される情報の整理【PJ補足資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'NotionDBに保存される情報の整理【PJ補足資料】', 'href': None}]}}, 'url': 'https://www.notion.so/NotionDB-PJ-1fdb52b17b3480eeb3a2ce404982c8f6', 'public_url': None}, {'object': 'page', 'id': '1fdb52b1-7b34-80b8-b66d-c5f4c2bf7448', 'created_time': '2025-05-24T10:10:00.000Z', 'last_edited_time': '2025-05-29T11:16:00.000Z', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4'}, 'cover': None, 'icon': None, 'parent': {'type': 'database_id', 'database_id': '1fdb52b1-7b34-800c-9a88-eabf06f74470'}, 'archived': False, 'in_trash': False, 'properties': {'ページ種類': {'id': '%3FSmd', 'type': 'multi_select', 'multi_select': [{'id': '80c23274-841a-45d3-a756-66791380e446', 'name': 'PJ概要資料', 'color': 'default'}]}, 'PJ_ステータス': {'id': 'Gu%3EV', 'type': 'status', 'status': {'id': 'f389936c-4434-4ef6-bd4d-7ce2861bc780', 'name': '未着手', 'color': 'default'}}, 'タスク_期限': {'id': 'KAfP', 'type': 'date', 'date': None}, 'アイデア_課題カテゴリ': {'id': 'K~%7DZ', 'type': 'multi_select', 'multi_select': []}, '更新日': {'id': 'R%3CMf', 'type': 'last_edited_time', 'last_edited_time': '2025-05-29T11:16:00.000Z'}, 'アイデア_優先度': {'id': 'RkB%5B', 'type': 'select', 'select': None}, 'PJ_担当者': {'id': 'SrBP', 'type': 'people', 'people': []}, 'PJ_期間': {'id': 'V_%7BR', 'type': 'date', 'date': None}, '関連PJ（NotionマスターDBとのリレーション）': {'id': '%5CRfY', 'type': 'relation', 'relation': [{'id': '1fdb52b1-7b34-80ee-b3a2-ce404982c8f6'}, {'id': '1fdb52b1-7b34-80e2-a0de-e1e82263c339'}], 'has_more': False}, '150字要約': {'id': '_SyM', 'type': 'rich_text', 'rich_text': [{'type': 'text', 'text': {'content': 'このプロジェクト概要資料は、スライドの高速生成を目的としたデータ基盤の構築に関するもので、技術検証やスプリント計画などが含まれています。利害関係者はBOC社内全体で、成功指標としてはテンプレートの完成や自動選定が挙げられています。リスクやコミュニケーション方針も明記されています。', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'このプロジェクト概要資料は、スライドの高速生成を目的としたデータ基盤の構築に関するもので、技術検証やスプリント計画などが含まれています。利害関係者はBOC社内全体で、成功指標としてはテンプレートの完成や自動選定が挙げられています。リスクやコミュニケーション方針も明記されています。', 'href': None}]}, 'アーカイブ': {'id': 'eUwS', 'type': 'checkbox', 'checkbox': True}, '議事録_会議種類': {'id': 'hIc_', 'type': 'multi_select', 'multi_select': []}, '作成日': {'id': 'jZy%3D', 'type': 'created_time', 'created_time': '2025-05-24T10:10:00.000Z'}, 'タスク＿担当者': {'id': 'o%5EpE', 'type': 'people', 'people': []}, 'タスク_ステータス': {'id': 'p~%40f', 'type': 'select', 'select': None}, '関連ドキュメント（NotionマスターDBとのリレーション）': {'id': 'q%5CkB', 'type': 'relation', 'relation': [], 'has_more': False}, '関連事業部': {'id': 'yKgx', 'type': 'multi_select', 'multi_select': []}, '最終更新者': {'id': 'zijq', 'type': 'last_edited_by', 'last_edited_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '作成者': {'id': '%7BsP%7C', 'type': 'created_by', 'created_by': {'object': 'user', 'id': '0583dca8-441b-4901-9deb-7baac386c1a4', 'name': '三橋直史', 'avatar_url': 'https://s3-us-west-2.amazonaws.com/public.notion-static.com/22513bd3-1e1c-4a51-bde2-60330a8aff3c/%E3%82%B9%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%B3%E3%82%B7%E3%83%A7%E3%83%83%E3%83%88_2023-02-06_16.01.32.png', 'type': 'person', 'person': {'email': '<EMAIL>'}}}, '名前': {'id': 'title', 'type': 'title', 'title': [{'type': 'text', 'text': {'content': 'Notionデータ基盤作成【PJ概要資料】', 'link': None}, 'annotations': {'bold': False, 'italic': False, 'strikethrough': False, 'underline': False, 'code': False, 'color': 'default'}, 'plain_text': 'Notionデータ基盤作成【PJ概要資料】', 'href': None}]}}, 'url': 'https://www.notion.so/Notion-PJ-1fdb52b17b3480b8b66dc5f4c2bf7448', 'public_url': None}], 'next_cursor': None, 'has_more': False, 'type': 'page_or_database', 'page_or_database': {}, 'request_id': 'b9170858-27fc-4eb5-8333-9ecb739c6d93'}