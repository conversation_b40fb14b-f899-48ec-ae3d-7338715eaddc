import os
from notion_client import Client
from langgraph_supervisor import create_supervisor
from notion_librarian.handoff import create_custom_handoff_tool
from notion_librarian.llm import model_supervisor
from notion_librarian.overview import overview_agent
from notion_librarian.feedback import feedback_agent

notion = Client(auth=os.getenv("NOTION_INTEGRATION_SECRET"))

try:
    with open("notion_librarian/prompts/supervisor.md", "r", encoding="utf-8") as file:
        prompt = file.read()
except FileNotFoundError:
    print("File not found")
except IOError as e:
    print(f"Error reading file: {e}")

DATABASE_ID = "1fdb52b17b34800c9a88eabf06f74470"


def get_notion_db_summary() -> list:
    """
    Notion DBの概要を取得する
    引数:
        None
    返戻値:
        list: DBの概要
    """
    try:
        response = notion.databases.query(database_id=DATABASE_ID)
        # Save the response to a log file
        with open("notion_librarian/log.txt", "w", encoding="utf-8") as f:
            f.write(str(response))
        return response["results"]
    except Exception as e:
        print(f"Error getting notion db summary: {e}")
        return []


graph = create_supervisor(
    model=model_supervisor,
    agents=[overview_agent, feedback_agent],
    tools=[
        get_notion_db_summary,
        create_custom_handoff_tool(
            agent_name="overview_agent",
            name="assign_overview_task",
            description="プロジェクト概要ページの作成をoverview_agentに依頼",
        ),
        create_custom_handoff_tool(
            agent_name="feedback_agent",
            name="assign_feedback_task",
            description="プロジェクト補足ページの作成をfeedback_agentに依頼",
        ),
    ],
    prompt=prompt,
    # parallel_tool_calls=True,
).compile()
