from langgraph.prebuilt import create_react_agent
from notion_librarian.llm import model_agent


def feedback(page_id: str) -> str:
    """Feedback agent"""
    return "You are a feedback agent.\n\n"


feedback_agent = create_react_agent(
    model=model_agent,
    tools=[feedback],
    prompt=(
        "You are a feedback agent.\n\n"
        "INSTRUCTIONS:\n"
        "- Assist ONLY with feedback-related tasks, DO NOT do any math\n"
        "- After you're done with your tasks, respond to the supervisor directly\n"
        "- Respond ONLY with the results of your work, do NOT include ANY other text."
    ),
    name="feedback_agent",
)
