import os
from notion_client import Client
from datetime import datetime  # Example for modifying a date property

# --- Configuration ---
# Best practice: Store your token in an environment variable
NOTION_TOKEN = os.getenv("NOTION_INTEGRATION_SECRET")
if not NOTION_TOKEN:
    raise ValueError("Please set the NOTION_TOKEN environment variable.")

# Replace with your actual IDs
TEMPLATE_PAGE_ID = "YOUR_TEMPLATE_PAGE_ID"  # e.g., "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TARGET_DATABASE_ID = (
    "YOUR_TARGET_DATABASE_ID"  # e.g., "yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy"
)

# Initialize the Notion client
notion = Client(auth=NOTION_TOKEN)


def get_template_data(template_id: str) -> tuple[dict, list]:
    """
    Retrieves the properties and content (blocks) of a template page.
    """
    print(f"Fetching template properties for page ID: {template_id}...")
    try:
        template_page = notion.pages.retrieve(page_id=template_id)
        template_properties = template_page.get("properties", {})
        print("Successfully fetched template properties.")
    except Exception as e:
        print(f"Error fetching template page properties: {e}")
        raise

    print(f"Fetching template content (blocks) for page ID: {template_id}...")
    try:
        template_blocks_response = notion.blocks.children.list(block_id=template_id)
        template_blocks = template_blocks_response.get("results", [])
        # The API returns blocks with IDs, which we don't want when creating new ones.
        # We need to strip out read-only fields like 'id', 'created_time', 'last_edited_time' etc.
        # and also potentially 'parent' if it's set to the template page.
        # For simplicity, this example might need further refinement for complex blocks.
        # A more robust solution would deeply iterate and clean the block objects.

        cleaned_template_blocks = []
        for block in template_blocks:
            # Create a copy to avoid modifying the original response object
            block_copy = block.copy()
            # Remove fields that shouldn't be present when creating new blocks
            block_copy.pop("id", None)
            block_copy.pop("parent", None)
            block_copy.pop("created_time", None)
            block_copy.pop("last_edited_time", None)
            block_copy.pop("created_by", None)
            block_copy.pop("last_edited_by", None)
            block_copy.pop("has_children", None)  # Let Notion recalculate this
            block_copy.pop("archived", None)

            # For specific block types, you might need to remove other read-only fields
            # or adjust their structure. Example: if block type has 'rich_text' with annotations
            # that are user-specific, you might need to clean those too.
            # This example keeps it simple.

            cleaned_template_blocks.append(block_copy)

        print(f"Successfully fetched {len(cleaned_template_blocks)} template blocks.")
        return template_properties, cleaned_template_blocks
    except Exception as e:
        print(f"Error fetching template blocks: {e}")
        raise


def create_page_from_template(
    database_id: str,
    template_properties: dict,
    template_blocks: list,
    new_page_data: dict,  # Data to override/add to template properties
):
    """
    Creates a new page in the specified database using the template's structure
    and overriding with new_page_data.
    """
    print(f"Preparing new page for database ID: {database_id}...")

    # --- 1. Adapt Properties ---
    new_properties = {}

    # Copy template properties, but ensure they are in the correct format for creation
    # The retrieve endpoint gives a slightly different format than the create/update endpoint.
    # We need to take the *values* of the properties.
    for prop_name, prop_data in template_properties.items():
        # Skip read-only properties like 'formula', 'rollup', 'created_time', etc.
        # Or properties you explicitly don't want to copy.
        # This is a simplified check; you might need a more robust way to identify writable properties.
        if prop_data["type"] not in [
            "formula",
            "rollup",
            "created_time",
            "last_edited_time",
            "created_by",
            "last_edited_by",
        ]:
            # For most property types, the value is directly usable.
            # However, for 'title', 'rich_text', 'select', 'multi_select', 'status', 'people', 'relation', 'files'
            # you need to ensure the format matches the 'create page' API schema.
            # The `retrieve` endpoint gives the value *within* the type key, e.g. "select": {"id": "...", "name": "...", "color": "..."}
            # The `create` endpoint expects just the value part, e.g., "select": {"name": "..."} or {"id": "..."}

            # Example: For a 'select' property, you'd typically want to set it by 'name' or 'id'.
            # If prop_data['type'] == 'select':
            #    if prop_data['select']: # if a value is set in template
            #        new_properties[prop_name] = {'select': {'name': prop_data['select']['name']}}
            # else:
            #    new_properties[prop_name] = {prop_data['type']: prop_data[prop_data['type']]}

            # A safer bet for many types is to just pass the inner value.
            # This example will try to directly use the structure from the retrieved properties,
            # which might need adjustments based on property types.
            # A more robust version would map each property type carefully.

            # For simplicity in this example, we're making a big assumption that most
            # property values can be reused if we extract the core value part.
            # This is often NOT the case and needs careful handling per property type.
            # For instance, a 'title' property retrieved looks like:
            # "Name": {"id": "title", "type": "title", "title": [{"type": "text", ...}]}
            # To create, you need:
            # "Name": {"title": [{"type": "text", ...}]}
            if prop_data["type"] == "title":
                new_properties[prop_name] = {"title": prop_data["title"]}
            elif prop_data["type"] == "rich_text":
                new_properties[prop_name] = {"rich_text": prop_data["rich_text"]}
            elif prop_data["type"] == "number":
                new_properties[prop_name] = {"number": prop_data["number"]}
            elif prop_data["type"] == "select" and prop_data.get("select"):
                new_properties[prop_name] = {
                    "select": {"id": prop_data["select"]["id"]}
                }  # or by name: {"name": prop_data["select"]["name"]}
            elif prop_data["type"] == "multi_select" and prop_data.get("multi_select"):
                new_properties[prop_name] = {
                    "multi_select": [
                        {"id": item["id"]} for item in prop_data["multi_select"]
                    ]
                }  # or by name
            elif prop_data["type"] == "status" and prop_data.get("status"):
                new_properties[prop_name] = {
                    "status": {"id": prop_data["status"]["id"]}
                }  # or by name
            elif prop_data["type"] == "date" and prop_data.get("date"):
                new_properties[prop_name] = {
                    "date": prop_data["date"]
                }  # e.g. {"start": "YYYY-MM-DD", "end": null}
            elif prop_data["type"] == "checkbox":
                new_properties[prop_name] = {"checkbox": prop_data["checkbox"]}
            elif prop_data["type"] == "url":
                new_properties[prop_name] = {"url": prop_data["url"]}
            elif prop_data["type"] == "email":
                new_properties[prop_name] = {"email": prop_data["email"]}
            elif prop_data["type"] == "phone_number":
                new_properties[prop_name] = {"phone_number": prop_data["phone_number"]}
            # Add other property types as needed (people, files, relation require more complex handling)

    # Override with new_page_data
    # new_page_data should be in the format expected by the API for creation
    # e.g., {"Name": {"title": [{"text": {"content": "My New Page Title"}}]}, "CustomDate": {"date": {"start": "2025-06-15"}}}
    for key, value in new_page_data.items():
        new_properties[key] = value

    print(f"Final properties for new page: {new_properties}")

    # --- 2. Adapt Content Blocks (Optional: Modify template_blocks here if needed) ---
    # For example, if a template block had placeholder text:
    # adapted_blocks = []
    # for block in template_blocks:
    #     if block.get("type") == "paragraph":
    #         # Make a deep copy to avoid modifying the original list of blocks for future calls
    #         import copy
    #         new_block = copy.deepcopy(block)
    #         new_block["paragraph"]["rich_text"][0]["text"]["content"] = "This is new dynamic content!"
    #         adapted_blocks.append(new_block)
    #     else:
    #         adapted_blocks.append(block)
    # For this example, we'll use the cleaned template blocks directly.
    adapted_blocks = template_blocks

    try:
        print("Creating new page...")
        created_page = notion.pages.create(
            parent={"database_id": database_id},
            properties=new_properties,
            children=adapted_blocks,  # Pass the cleaned/adapted blocks
        )
        print(f"Successfully created page! Page ID: {created_page['id']}")
        print(f"Link: {created_page.get('url')}")
        return created_page
    except Exception as e:
        print(f"Error creating page: {e}")
        # The Notion API often returns detailed error messages in e.body or e.message
        if hasattr(e, "body"):
            print(f"Error body: {e.body}")
        raise


if __name__ == "__main__":
    if (
        TEMPLATE_PAGE_ID == "YOUR_TEMPLATE_PAGE_ID"
        or TARGET_DATABASE_ID == "YOUR_TARGET_DATABASE_ID"
    ):
        print(
            "Please update TEMPLATE_PAGE_ID and TARGET_DATABASE_ID with your actual IDs."
        )
    else:
        try:
            # 1. Get template structure
            props, blocks = get_template_data(TEMPLATE_PAGE_ID)

            # 2. Define the specific data for your new page
            # This will override any properties from the template with the same name
            # or add new ones if they don't exist in the template (but are in the DB schema).
            current_time_iso = datetime.now().isoformat()
            new_entry_specific_data = {
                "Name": {  # Assuming 'Name' is the title property in your database
                    "title": [
                        {
                            "text": {
                                "content": f"New Entry from Template - {current_time_iso}"
                            }
                        }
                    ]
                },
                # Example: If your database has a 'Custom Text' property (type: rich_text)
                # "Custom Text": {
                #     "rich_text": [
                #         {
                #             "text": {
                #                 "content": "Specific information for this new entry."
                #             }
                #         }
                #     ]
                # },
                # Example: If your database has a 'Due Date' property (type: date)
                # "Due Date": {
                #     "date": {
                #         "start": "2025-12-31"
                #     }
                # }
                # Example: If your template has a 'Status' (select/status type) and you want to keep it or change it
                # If the template already set 'Status', and you want to keep it, you don't need to specify it here
                # unless you are changing it from the template's default.
                # "Status": { # Assuming 'Status' is a 'status' or 'select' property
                # "status": {"name": "In Progress"} # or "select": {"name": "In Progress"}
                # }
            }

            # 3. Create the new page
            create_page_from_template(
                TARGET_DATABASE_ID, props, blocks, new_entry_specific_data
            )

        except Exception as e:
            print(f"An error occurred in the main script: {e}")
