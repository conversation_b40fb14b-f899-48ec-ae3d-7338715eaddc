## PJ 憲章

### 目的・理想

社内の問い合わせ対応を自動化し、従業員の生産性向上と顧客満足度の向上を目指す。

---

### 背景・課題

- 問い合わせ対応に多くの工数がかかっている
- ナレッジの属人化が進んでいる
- 顧客対応の品質にばらつきがある

---

### 成功指標

- 問い合わせ対応時間が 30%短縮
- チャットボットの利用率 70%以上
- 顧客満足度アンケートで 80 点以上

---

### 利害関係者

- プロジェクトオーナー（情報システム部長）
- サポート部門
- 開発チーム
- 顧客

---

### チーム構成

- プロジェクトマネージャー：田中
- 開発リーダー：佐藤
- 開発メンバー：鈴木、山田
- サポート担当：高橋

---

### 制約・前提

- 既存の社内システムと連携すること
- 3 ヶ月以内にリリース
- 予算 500 万円以内

---

### 想定リスク・回避方法

- チャットボットの精度不足 → 定期的なチューニングと FAQ の拡充
- システム連携の遅延 → 早期に技術検証を実施
- 利用率が伸びない → 社内説明会やマニュアル整備

---

### コミュニケーション方針

- 週次で定例ミーティングを実施
- Slack で日次進捗共有
- 重要事項はメールで全体連絡

---

## PJ スケジュール

- 4 月：要件定義・設計
- 5 月：開発・テスト
- 6 月：リリース・運用開始

---

## スプリント計画

### ステップ

1. 要件定義
2. システム設計
3. 開発
4. テスト
5. リリース

### 技術検証

明らかにすること

- チャットボット API の精度
- 社内システムとの連携可否

- 調査

- 既存 FAQ データの整備状況
- セキュリティ要件

---

### スプリント 1

| セクション                 | ひと言で書くこと                   | 内容                                                                   |
| -------------------------- | ---------------------------------- | ---------------------------------------------------------------------- |
| **1. ゴール**(Sprint Goal) | チャットボットの基本機能を実装     | FAQ 自動応答、簡易 UI の作成                                           |
| **2. やること**(What)      | 実装・調査など具体タスクを 3〜5 件 | 1. FAQ データの整備<br>2. チャット UI 作成<br>3. API 連携<br>4. テスト |
| **3. 担当**(Who)           | 各タスクのメイン担当＋協力者       | 田中（FAQ）、佐藤（UI）、鈴木（API）、山田（テスト）                   |
| **4. リスク**(Risk)        | 詰まりそうな要因と一次対応         | API 仕様変更 → 早期に仕様確認、FAQ 不足 → サポート部門と連携           |

メモ：

- 初回スプリントで基本機能を固める
- 次スプリントで応用機能・運用準備へ
